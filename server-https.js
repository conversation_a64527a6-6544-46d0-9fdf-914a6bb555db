const express = require('express');
const https = require('https');
const http = require('http');
const socketIo = require('socket.io');
const path = require('path');
const cors = require('cors');
const fs = require('fs');

const app = express();

// Middleware
app.use(cors());
app.use(express.static('public'));

// Routes
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

app.get('/mobile', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'mobile.html'));
});

app.get('/pc', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'pc.html'));
});

// Vytvoření self-signed certifikátu pro testování
function createSelfSignedCert() {
  const selfsigned = require('selfsigned');
  const attrs = [{ name: 'commonName', value: 'localhost' }];
  const pems = selfsigned.generate(attrs, { days: 365 });
  
  return {
    key: pems.private,
    cert: pems.cert
  };
}

// Pokus o načtení existujících certifikátů, jinak vytvoř self-signed
let httpsOptions;
try {
  httpsOptions = {
    key: fs.readFileSync('server.key'),
    cert: fs.readFileSync('server.crt')
  };
  console.log('Používám existující SSL certifikáty');
} catch (error) {
  console.log('SSL certifikáty nenalezeny, vytvářím self-signed certifikát...');
  try {
    httpsOptions = createSelfSignedCert();
    console.log('Self-signed certifikát vytvořen');
  } catch (err) {
    console.log('Nelze vytvořit self-signed certifikát, spouštím pouze HTTP server');
    httpsOptions = null;
  }
}

// Vytvoření serverů
const httpServer = http.createServer(app);
let httpsServer = null;

if (httpsOptions) {
  httpsServer = https.createServer(httpsOptions, app);
}

// Socket.IO konfigurace
const io = socketIo(httpsServer || httpServer, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Socket.IO pro WebRTC signaling
io.on('connection', (socket) => {
  console.log('Uživatel se připojil:', socket.id);

  socket.on('join-room', (roomId, userType) => {
    socket.join(roomId);
    socket.userType = userType;
    console.log(`${userType} se připojil do místnosti: ${roomId}`);
    
    socket.to(roomId).emit('user-connected', socket.id, userType);
  });

  socket.on('offer', (offer, roomId) => {
    socket.to(roomId).emit('offer', offer, socket.id);
  });

  socket.on('answer', (answer, roomId) => {
    socket.to(roomId).emit('answer', answer, socket.id);
  });

  socket.on('ice-candidate', (candidate, roomId) => {
    socket.to(roomId).emit('ice-candidate', candidate, socket.id);
  });

  socket.on('disconnect', () => {
    console.log('Uživatel se odpojil:', socket.id);
  });
});

// Spuštění serverů
const HTTP_PORT = process.env.HTTP_PORT || 3000;
const HTTPS_PORT = process.env.HTTPS_PORT || 3443;

httpServer.listen(HTTP_PORT, () => {
  console.log(`HTTP Server běží na portu ${HTTP_PORT}`);
  console.log(`HTTP: http://localhost:${HTTP_PORT}`);
});

if (httpsServer) {
  httpsServer.listen(HTTPS_PORT, () => {
    console.log(`HTTPS Server běží na portu ${HTTPS_PORT}`);
    console.log(`HTTPS: https://localhost:${HTTPS_PORT}`);
    console.log('Pro self-signed certifikát budete muset přijmout bezpečnostní varování v prohlížeči');
  });
} else {
  console.log('HTTPS server není dostupný - nainstalujte "selfsigned" balíček: npm install selfsigned');
}
