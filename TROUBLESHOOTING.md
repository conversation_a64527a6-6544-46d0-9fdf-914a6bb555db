# 🔧 Řešení problémů s kamerou

## ❌ Kamera nefunguje - nejčastější příčiny a řešení

### 1. **Povolení pro kameru**
**Problém:** Prohlížeč blokuje přístup ke kameře
**Řešení:**
- Klikněte na ikonu zámku/kamery v adresním řádku
- Povolte přístup ke kameře a mikrofonu
- Obnovte stránku (F5)

### 2. **HTTPS požadavek**
**Problém:** Moderní prohlížeče vyžadují HTTPS pro kameru (kromě localhost)
**Řešení:**
```bash
# Spusťte HTTPS verzi serveru
npm run https
```
Pak otevřete: https://localhost:3443/mobile
- Přijměte bezpečnostní varování (self-signed certifikát)

### 3. **Kamera je používána jinou aplikací**
**Problém:** <PERSON><PERSON> (Zoom, Teams, atd.) používá kameru
**Řešení:**
- Zavřete všechny aplikace používající kameru
- Restartujte prohlížeč

### 4. **Nepodporovaný prohlížeč**
**Problém:** Starý prohlížeč nepodporuje WebRTC
**Řešení:**
- Použijte Chrome, Firefox, Safari nebo Edge
- Aktualizujte prohlížeč na nejnovější verzi

### 5. **Mobilní zařízení - specifické problémy**

#### iPhone/iPad:
- Musí být iOS 11+ 
- Použijte Safari (ne Chrome na iOS)
- Stránka musí být načtena přes HTTPS

#### Android:
- Použijte Chrome nebo Firefox
- Povolte kameru v nastavení prohlížeče
- Zkontrolujte oprávnění aplikace

## 🔍 Diagnostika

### Otevřete konzoli prohlížeče:
1. **Chrome/Edge:** F12 → Console
2. **Firefox:** F12 → Console  
3. **Safari:** Cmd+Option+C

### Hledejte tyto chyby:

**"NotAllowedError"**
→ Povolte kameru v nastavení prohlížeče

**"NotFoundError"** 
→ Kamera nebyla nalezena, zkontrolujte připojení

**"NotSupportedError"**
→ Prohlížeč nepodporuje kameru, zkuste jiný

**"NotReadableError"**
→ Kamera je používána jinou aplikací

**"HTTPS required"**
→ Spusťte HTTPS server: `npm run https`

## 🧪 Test kamery

Otevřete tuto stránku pro test kamery:
```
https://webrtc.github.io/samples/src/content/getusermedia/gum/
```

Pokud ani zde kamera nefunguje, problém je v systému/prohlížeči, ne v naší aplikaci.

## 📱 Mobilní testování

### Testování v lokální síti:
1. Zjistěte IP adresu PC: `ipconfig` (Windows) nebo `ifconfig` (Mac/Linux)
2. Na telefonu otevřete: `http://[IP-ADRESA]:3000/mobile`
3. Příklad: `http://*************:3000/mobile`

### Pro HTTPS v lokální síti:
1. Spusťte: `npm run https`
2. Na telefonu: `https://[IP-ADRESA]:3443/mobile`
3. Přijměte bezpečnostní varování

## 🔄 Rychlé řešení

Pokud nic nefunguje, zkuste v tomto pořadí:

1. **Obnovte stránku** (F5)
2. **Zavřete a znovu otevřete prohlížeč**
3. **Restartujte server** (Ctrl+C, pak `npm start`)
4. **Zkuste jiný prohlížeč**
5. **Spusťte HTTPS verzi** (`npm run https`)
6. **Zkuste z jiného zařízení**

## 📞 Pokud stále nefunguje

1. Otevřete konzoli prohlížeče (F12)
2. Zkopírujte chybové hlášky
3. Zkontrolujte, že:
   - Server běží na portu 3000
   - Firewall neblokuje připojení
   - Antivirus neblokuje kameru
   - Máte funkční kameru (otestujte v jiné aplikaci)

## 🌐 Síťové problémy

### Lokální síť:
- Oba zařízení musí být ve stejné WiFi síti
- Zkontrolujte firewall na PC

### Internet:
- Potřebujete veřejnou IP nebo port forwarding
- HTTPS je povinné
- Možná budete potřebovat TURN server pro NAT traversal
