# 📱➡️💻 Video Sharing App

Node.js aplikace pro sdílení videa z telefonu na PC v reálném čase pomocí WebRTC.

## 🚀 Jak spustit

1. **Nainstalovat závislosti:**
   ```bash
   npm install
   ```

2. **Spustit server:**
   ```bash
   npm start
   ```
   
   Nebo pro development s automatickým restartováním:
   ```bash
   npm run dev
   ```

3. **Otevřít v prohlížeči:**
   - Hlavní stránka: http://localhost:3000
   - PC zobrazení: http://localhost:3000/pc
   - Mobilní sdílení: http://localhost:3000/mobile

## 📖 Jak používat

### Krok 1: Příprava PC
1. Na PC otevřete: http://localhost:3000/pc
2. Zadejte ID místnosti (např. "room123")
3. Stránka bude čekat na připojení telefonu

### Krok 2: Připojení telefonu
1. Na telefonu otevřete: http://localhost:3000/mobile
2. Zadejte stejné ID místnosti jako na PC
3. Klikněte na "Spustit sdílení"
4. Povolte přístup ke kameře a mikrofonu

### Krok 3: Sledování
- Video z telefonu se automaticky zobrazí na PC
- Na PC můžete přepnout do režimu celé obrazovky
- Kvalita videa se automaticky přizpůsobí rychlosti připojení

## 🔧 Funkce

- **Real-time video streaming** pomocí WebRTC
- **Automatické připojení** mezi zařízeními
- **Responzivní design** pro mobil i PC
- **Celá obrazovka** na PC
- **Automatické obnovení** při ztrátě spojení
- **Místnosti** pro více uživatelů současně

## 🌐 Síťové požadavky

- **Lokální síť:** Aplikace funguje v lokální síti bez dalších nastavení
- **Internet:** Pro připojení přes internet potřebujete:
  - Veřejnou IP adresu nebo port forwarding
  - HTTPS certifikát (pro přístup ke kameře)
  - TURN server pro NAT traversal (volitelné)

## 🔒 HTTPS pro produkci

Pro použití přes internet potřebujete HTTPS. Vytvořte `server-https.js`:

```javascript
const https = require('https');
const fs = require('fs');
// ... zbytek kódu ze server.js

const options = {
  key: fs.readFileSync('path/to/private-key.pem'),
  cert: fs.readFileSync('path/to/certificate.pem')
};

https.createServer(options, app).listen(443, () => {
  console.log('HTTPS Server běží na portu 443');
});
```

## 🛠️ Technologie

- **Backend:** Node.js, Express, Socket.IO
- **Frontend:** HTML5, CSS3, JavaScript
- **Video:** WebRTC, getUserMedia API
- **Real-time komunikace:** WebSockets

## 📱 Podporované prohlížeče

- **Chrome/Chromium** (doporučeno)
- **Firefox**
- **Safari** (iOS 11+)
- **Edge**

## 🐛 Řešení problémů

### Kamera nefunguje
- Zkontrolujte povolení pro kameru v prohlížeči
- Na iOS/Android musí být stránka načtena přes HTTPS
- Zkuste obnovit stránku

### Video se nezobrazuje
- Zkontrolujte, že obě zařízení používají stejné ID místnosti
- Obnovte obě stránky
- Zkontrolujte konzoli prohlížeče pro chyby

### Pomalé připojení
- Aplikace automaticky přizpůsobuje kvalitu
- Zkuste se připojit k lepší WiFi síti
- Zavřete ostatní aplikace používající internet

## 📄 Licence

MIT License
