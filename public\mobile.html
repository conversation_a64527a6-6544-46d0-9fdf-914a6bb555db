<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 <PERSON>bil<PERSON>í sd<PERSON></title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        video {
            width: 100%;
            max-width: 400px;
            border-radius: 10px;
            margin: 20px 0;
        }
        button {
            padding: 15px 30px;
            margin: 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 200px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #cccccc;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 10px;
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.2);
        }
        .connected {
            background: rgba(76, 175, 80, 0.3);
        }
        .error {
            background: rgba(244, 67, 54, 0.3);
        }
        .room-info {
            background: rgba(255, 255, 255, 0.2);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Mobilní sdílení</h1>
        
        <div class="room-info">
            <strong>Místnost:</strong> <span id="roomDisplay"></span>
        </div>
        
        <video id="localVideo" autoplay muted playsinline></video>
        
        <div>
            <button id="startBtn" onclick="startSharing()">Spustit sdílení</button>
            <button id="stopBtn" onclick="stopSharing()" disabled>Zastavit sdílení</button>
        </div>
        
        <div id="status" class="status">
            Připraveno k sdílení
        </div>
        
        <div style="margin-top: 20px;">
            <a href="/" style="color: white;">← Zpět na hlavní stránku</a>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script>
        const socket = io();
        let localStream = null;
        let peerConnection = null;
        let roomId = new URLSearchParams(window.location.search).get('room') || 'room123';

        // Kontrola podpory WebRTC a kamery
        function checkSupport() {
            const issues = [];

            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                issues.push('Váš prohlížeč nepodporuje přístup ke kameře');
            }

            if (!window.RTCPeerConnection) {
                issues.push('Váš prohlížeč nepodporuje WebRTC');
            }

            if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
                issues.push('Pro kameru je vyžadováno HTTPS připojení (kromě localhost)');
            }

            if (issues.length > 0) {
                updateStatus('Problémy: ' + issues.join(', '), 'error');
                startBtn.disabled = true;
                return false;
            }

            return true;
        }
        
        document.getElementById('roomDisplay').textContent = roomId;

        // Kontrola podpory při načtení stránky
        window.addEventListener('load', () => {
            if (checkSupport()) {
                updateStatus('Připraveno k sdílení - klikněte na "Spustit sdílení"', '');
            }
        });
        
        const localVideo = document.getElementById('localVideo');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        
        // WebRTC konfigurace
        const configuration = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' }
            ]
        };
        
        function updateStatus(message, type = '') {
            status.textContent = message;
            status.className = 'status ' + type;
        }
        
        async function startSharing() {
            try {
                updateStatus('Získávání přístupu ke kameře...', '');

                // Zkusíme různé konfigurace kamery
                let constraints = [
                    // Nejdříve zkusíme zadní kameru s vysokým rozlišením
                    {
                        video: {
                            facingMode: 'environment',
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        },
                        audio: true
                    },
                    // Pak zkusíme přední kameru
                    {
                        video: {
                            facingMode: 'user',
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        },
                        audio: true
                    },
                    // Základní konfigurace bez specifikace kamery
                    {
                        video: {
                            width: { ideal: 640 },
                            height: { ideal: 480 }
                        },
                        audio: true
                    },
                    // Jen video bez audia
                    {
                        video: true,
                        audio: false
                    },
                    // Minimální konfigurace
                    {
                        video: true
                    }
                ];

                let streamObtained = false;

                for (let i = 0; i < constraints.length && !streamObtained; i++) {
                    try {
                        console.log(`Zkouším konfiguraci ${i + 1}:`, constraints[i]);
                        localStream = await navigator.mediaDevices.getUserMedia(constraints[i]);
                        streamObtained = true;
                        console.log('Úspěšně získán stream s konfigurací:', constraints[i]);
                    } catch (err) {
                        console.log(`Konfigurace ${i + 1} selhala:`, err.message);
                        if (i === constraints.length - 1) {
                            throw err; // Pokud ani poslední konfigurace nefunguje
                        }
                    }
                }

                localVideo.srcObject = localStream;

                // Připojit se k místnosti
                socket.emit('join-room', roomId, 'mobile');

                startBtn.disabled = true;
                stopBtn.disabled = false;

                updateStatus('Čekání na připojení PC...', '');

            } catch (error) {
                console.error('Chyba při získávání média:', error);
                let errorMessage = 'Chyba: ';

                if (error.name === 'NotAllowedError') {
                    errorMessage += 'Přístup ke kameře byl zamítnut. Povolte kameru v nastavení prohlížeče.';
                } else if (error.name === 'NotFoundError') {
                    errorMessage += 'Kamera nebyla nalezena.';
                } else if (error.name === 'NotSupportedError') {
                    errorMessage += 'Kamera není podporována.';
                } else if (error.name === 'NotReadableError') {
                    errorMessage += 'Kamera je používána jinou aplikací.';
                } else if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
                    errorMessage += 'Pro kameru je vyžadováno HTTPS připojení.';
                } else {
                    errorMessage += error.message || 'Neznámá chyba při přístupu ke kameře.';
                }

                updateStatus(errorMessage, 'error');
            }
        }
        
        function stopSharing() {
            if (localStream) {
                localStream.getTracks().forEach(track => track.stop());
                localStream = null;
            }
            
            if (peerConnection) {
                peerConnection.close();
                peerConnection = null;
            }
            
            localVideo.srcObject = null;
            startBtn.disabled = false;
            stopBtn.disabled = true;
            
            updateStatus('Sdílení zastaveno', '');
        }
        
        // Socket.IO události
        socket.on('user-connected', (userId, userType) => {
            if (userType === 'pc' && localStream) {
                updateStatus('PC se připojilo, navazování spojení...', '');
                createPeerConnection();
            }
        });
        
        socket.on('offer', async (offer, userId) => {
            // Mobile neočekává offer, PC vytváří offer
        });
        
        socket.on('answer', async (answer, userId) => {
            if (peerConnection) {
                await peerConnection.setRemoteDescription(answer);
                updateStatus('Spojení navázáno! Video se sdílí.', 'connected');
            }
        });
        
        socket.on('ice-candidate', async (candidate, userId) => {
            if (peerConnection) {
                await peerConnection.addIceCandidate(candidate);
            }
        });
        
        async function createPeerConnection() {
            peerConnection = new RTCPeerConnection(configuration);
            
            // Přidat lokální stream
            localStream.getTracks().forEach(track => {
                peerConnection.addTrack(track, localStream);
            });
            
            // ICE kandidáti
            peerConnection.onicecandidate = (event) => {
                if (event.candidate) {
                    socket.emit('ice-candidate', event.candidate, roomId);
                }
            };
            
            // Vytvořit offer
            const offer = await peerConnection.createOffer();
            await peerConnection.setLocalDescription(offer);
            socket.emit('offer', offer, roomId);
        }
    </script>
</body>
</html>
